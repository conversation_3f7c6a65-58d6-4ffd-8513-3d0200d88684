<!--
  ClaimList.svelte
  
  A responsive insurance claim list component that displays customer claims in a card layout.
  
  Features:
  - Responsive CSS Grid layout (1-4 cards per row based on screen size)
  - Tailwind CSS styling with hover effects and transitions
  - Accessible markup with ARIA labels and semantic HTML
  - Color-coded status badges
  - Professional card design with claim details
  
  Responsive Breakpoints:
  - Mobile (< 640px): 1 card per row
  - Tablet (640px - 1024px): 2 cards per row  
  - Desktop (1024px - 1440px): 3 cards per row
  - Large screens (> 1440px): 4 cards per row
  
  Usage:
  <ClaimList on:navigate />
-->

<script>
  import { onMount, createEventDispatcher } from "svelte";

  const dispatch = createEventDispatcher();

  // Mock data structure for sample claims
  const claims = [
    {
      id: "claim-001",
      claimNumber: "CLM-***********",
      type: "Auto",
      status: "Approved",
      amount: 2500.00,
      deductible: 500.00,
      dateReported: "2024-08-15T10:30:00Z",
      dateOfLoss: "2024-08-12T14:45:00Z",
      description: "Minor collision repair - rear-end accident at intersection",
      policyNumber: "AUTO-***********"
    },
    {
      id: "claim-002", 
      claimNumber: "CLM-***********",
      type: "Home",
      status: "Processing",
      amount: 8500.00,
      deductible: 1000.00,
      dateReported: "2024-09-22T09:15:00Z",
      dateOfLoss: "2024-09-20T03:30:00Z",
      description: "Water damage from burst pipe in basement",
      policyNumber: "HOME-***********"
    },
    {
      id: "claim-003",
      claimNumber: "CLM-***********", 
      type: "Health",
      status: "Denied",
      amount: 1250.00,
      deductible: 250.00,
      dateReported: "2024-07-10T11:45:00Z",
      dateOfLoss: "2024-07-08T00:00:00Z",
      description: "Emergency room visit for sports injury",
      policyNumber: "HEALTH-***********"
    },
    {
      id: "claim-004",
      claimNumber: "CLM-***********",
      type: "Auto",
      status: "Under Review",
      amount: 3200.00,
      deductible: 500.00,
      dateReported: "2024-10-01T14:20:00Z",
      dateOfLoss: "2024-09-28T16:30:00Z",
      description: "Windshield replacement and front bumper damage",
      policyNumber: "AUTO-***********"
    },
    {
      id: "claim-005",
      claimNumber: "CLM-***********",
      type: "Home",
      status: "Approved",
      amount: 5200.00,
      deductible: 1000.00,
      dateReported: "2024-06-15T11:30:00Z",
      dateOfLoss: "2024-06-12T22:45:00Z",
      description: "Storm damage to roof and siding",
      policyNumber: "HOME-***********"
    },
    {
      id: "claim-006",
      claimNumber: "CLM-***********",
      type: "Health",
      status: "Processing",
      amount: 890.00,
      deductible: 200.00,
      dateReported: "2024-09-30T08:45:00Z",
      dateOfLoss: "2024-09-28T00:00:00Z",
      description: "Routine medical procedure and follow-up care",
      policyNumber: "HEALTH-***********"
    }
  ];

  // Claim type icons mapping
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Health: "🏥",
    Life: "❤️",
    Business: "🏢",
  };

  // Status color mapping
  const statusColors = {
    "Approved": "bg-green-100 text-green-800 border-green-200",
    "Processing": "bg-yellow-100 text-yellow-800 border-yellow-200", 
    "Denied": "bg-red-100 text-red-800 border-red-200",
    "Under Review": "bg-blue-100 text-blue-800 border-blue-200",
    "Pending": "bg-gray-100 text-gray-800 border-gray-200"
  };

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  }

  // Format date
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });
  }

  // Handle claim card click to navigate to detail page
  function handleClaimClick(claimId) {
    dispatch("navigate", {
      page: "claim-detail",
      claimId: claimId,
    });
  }

  onMount(() => {
    console.log("ClaimList component mounted");
    console.log("Loaded claims:", claims.length);
    console.log("Claim data:", claims);
  });
</script>

<main class="p-4" role="main" aria-label="Insurance Claim List">
  <header class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      Your Insurance Claims
    </h1>
    <p class="text-gray-600">
      Track and manage all your insurance claims
    </p>
  </header>

  <section
    class="grid gap-6
           grid-cols-1
           sm:grid-cols-2
           lg:grid-cols-3
           xl:grid-cols-4"
    aria-label="Claim cards grid"
  >
    {#each claims as claim (claim.id)}
      <button
        class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               hover:scale-102 transform
               border border-gray-100
               min-h-[280px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
        aria-labelledby="claim-{claim.id}-title"
        on:click={() => handleClaimClick(claim.id)}
      >
        <!-- Claim Header -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-3">
            <h2
              id="claim-{claim.id}-title"
              class="text-lg font-semibold text-gray-900 flex items-center gap-2"
            >
              <span class="text-2xl" aria-hidden="true"
                >{typeIcons[claim.type]}</span
              >
              {claim.type}
            </h2>
            <span
              class="px-3 py-1 rounded-full text-xs font-medium border
                     {statusColors[claim.status]}"
              aria-label="Claim status: {claim.status}"
            >
              {claim.status}
            </span>
          </div>
          <p class="text-sm text-gray-600 font-mono">
            {claim.claimNumber}
          </p>
        </div>

        <!-- Claim Details -->
        <div class="space-y-3 mb-4 flex-grow">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Claim Amount:</span>
            <span class="text-sm font-medium text-gray-900">
              {formatCurrency(claim.amount)}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Deductible:</span>
            <span class="text-sm font-medium text-gray-900">
              {formatCurrency(claim.deductible)}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Date of Loss:</span>
            <span class="text-sm font-medium text-gray-900">
              {formatDate(claim.dateOfLoss)}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Policy:</span>
            <span class="text-sm font-medium text-gray-900 font-mono">
              {claim.policyNumber}
            </span>
          </div>
        </div>

        <!-- Claim Description -->
        <div class="border-t border-gray-100 pt-3">
          <p class="text-sm text-gray-600 line-clamp-2">
            {claim.description}
          </p>
        </div>
      </button>
    {/each}
  </section>
</main>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .hover\:scale-102:hover {
    transform: scale(1.02);
  }
</style>

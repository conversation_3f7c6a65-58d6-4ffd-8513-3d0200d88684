<!--
  PolicyList.svelte
  
  A responsive insurance policy list component that displays customer policies in a card layout.
  
  Features:
  - Responsive CSS Grid layout (1-4 cards per row based on screen size)
  - Tailwind CSS styling with hover effects and transitions
  - Accessible markup with ARIA labels and semantic HTML
  - Color-coded status badges
  - Professional card design with policy details
  
  Responsive Breakpoints:
  - Mobile (< 640px): 1 card per row
  - Tablet (640px - 1024px): 2 cards per row  
  - Desktop (1024px - 1440px): 3 cards per row
  - Large screens (> 1440px): 4 cards per row
  
  Usage:
  <PolicyList />
  
  Future Enhancement: Accept policies as props
  <PolicyList {policies} />
-->

<script>
  import { onMount, createEventDispatcher } from "svelte";

  const dispatch = createEventDispatcher();

  // Mock data structure for 10 sample policies
  const policies = [
    {
      id: "pol-001",
      policyNumber: "AUTO-2024-001234",
      type: "Auto",
      coverageAmount: 250000,
      monthlyPremium: 185.5,
      status: "Active",
      expirationDate: "2025-06-15T00:00:00Z",
      description:
        "Comprehensive auto coverage including collision, liability, and uninsured motorist protection.",
    },
    {
      id: "pol-002",
      policyNumber: "HOME-2024-005678",
      type: "Home",
      coverageAmount: 450000,
      monthlyPremium: 125.75,
      status: "Active",
      expirationDate: "2025-03-22T00:00:00Z",
      description:
        "Full homeowners insurance with dwelling, personal property, and liability coverage.",
    },
    {
      id: "pol-003",
      policyNumber: "LIFE-***********",
      type: "Life",
      coverageAmount: 500000,
      monthlyPremium: 89.25,
      status: "Pending",
      expirationDate: "2025-12-31T00:00:00Z",
      description:
        "Term life insurance policy providing financial security for beneficiaries.",
    },
    {
      id: "pol-004",
      policyNumber: "HEALTH-***********",
      type: "Health",
      coverageAmount: 1000000,
      monthlyPremium: 425.0,
      status: "Active",
      expirationDate: "2024-12-31T00:00:00Z",
      description:
        "Comprehensive health insurance with medical, dental, and vision coverage.",
    },
    {
      id: "pol-005",
      policyNumber: "BIZ-***********",
      type: "Business",
      coverageAmount: 2000000,
      monthlyPremium: 750.0,
      status: "Expired",
      expirationDate: "2024-01-15T00:00:00Z",
      description:
        "General liability and property insurance for small business operations.",
    },
    {
      id: "pol-006",
      policyNumber: "AUTO-***********",
      type: "Auto",
      coverageAmount: 300000,
      monthlyPremium: 210.25,
      status: "Cancelled",
      expirationDate: "2024-08-30T00:00:00Z",
      description:
        "Premium auto insurance with comprehensive coverage and roadside assistance.",
    },
    {
      id: "pol-007",
      policyNumber: "HOME-***********",
      type: "Home",
      coverageAmount: 650000,
      monthlyPremium: 195.5,
      status: "Pending",
      expirationDate: "2025-09-10T00:00:00Z",
      description:
        "Enhanced homeowners policy including flood and earthquake protection.",
    },
    {
      id: "pol-008",
      policyNumber: "LIFE-***********",
      type: "Life",
      coverageAmount: 750000,
      monthlyPremium: 145.75,
      status: "Active",
      expirationDate: "2025-11-20T00:00:00Z",
      description:
        "Whole life insurance with cash value accumulation and investment options.",
    },
    {
      id: "pol-009",
      policyNumber: "HEALTH-***********",
      type: "Health",
      coverageAmount: 500000,
      monthlyPremium: 325.0,
      status: "Expired",
      expirationDate: "2024-02-28T00:00:00Z",
      description:
        "Basic health insurance plan with essential medical coverage and prescription benefits.",
    },
    {
      id: "pol-010",
      policyNumber: "BIZ-***********",
      type: "Business",
      coverageAmount: 1500000,
      monthlyPremium: 580.25,
      status: "Cancelled",
      expirationDate: "2024-05-15T00:00:00Z",
      description:
        "Professional liability insurance for service-based business with cyber coverage.",
    },
  ];

  // Policy type icons mapping
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Life: "❤️",
    Health: "🏥",
    Business: "🏢",
  };

  // Status color classes for badges
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  // Format currency with decimals for premiums
  function formatPremium(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  // Format date
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });
  }

  // Handle policy card click to navigate to detail page
  function handlePolicyClick(policyId) {
    dispatch("navigate", {
      page: "policy-detail",
      policyId: policyId,
    });
  }

  onMount(() => {
    console.log("PolicyList component mounted");
    console.log("Loaded policies:", policies.length);
    console.log("Policy data:", policies);
  });
</script>

<main class="p-4" role="main" aria-label="Insurance Policy List">
  <header class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      Your Insurance Policies
    </h1>
    <p class="text-gray-600">
      Manage and view all your active insurance policies
    </p>
  </header>

  <section
    class="grid gap-6
           grid-cols-1
           sm:grid-cols-2
           lg:grid-cols-3
           xl:grid-cols-4"
    aria-label="Policy cards grid"
  >
    {#each policies as policy (policy.id)}
      <button
        class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               hover:scale-102 transform
               border border-gray-100
               min-h-[280px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
        aria-labelledby="policy-{policy.id}-title"
        on:click={() => handlePolicyClick(policy.id)}
      >
        <!-- Policy Header -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-3">
            <h2
              id="policy-{policy.id}-title"
              class="text-lg font-semibold text-gray-900 flex items-center gap-2"
            >
              <span class="text-2xl" aria-hidden="true"
                >{typeIcons[policy.type]}</span
              >
              {policy.type}
            </h2>
            <span
              class="px-3 py-1 rounded-full text-xs font-medium border
                     {statusColors[policy.status]}"
              aria-label="Policy status: {policy.status}"
            >
              {policy.status}
            </span>
          </div>

          <p class="text-sm text-gray-600 mb-2">
            Policy #{policy.policyNumber}
          </p>
        </div>

        <!-- Policy Details -->
        <div class="space-y-3 mb-4 flex-grow">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Coverage Amount:</span>
            <span class="text-sm font-medium text-gray-900">
              {formatCurrency(policy.coverageAmount)}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Monthly Premium:</span>
            <span class="text-sm font-medium text-gray-900">
              {formatPremium(policy.monthlyPremium)}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">Expires:</span>
            <span class="text-sm font-medium text-gray-900">
              {formatDate(policy.expirationDate)}
            </span>
          </div>
        </div>

        <!-- Policy Description -->
        <div class="border-t border-gray-100 pt-4">
          <p class="text-xs text-gray-600 leading-relaxed line-clamp-3">
            {policy.description}
          </p>
        </div>
      </button>
    {/each}
  </section>
</main>

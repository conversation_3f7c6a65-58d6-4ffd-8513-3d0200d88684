<!--
  ClaimDetail.svelte

  Comprehensive claim detail component that displays detailed claim information.

  Features:
  - Comprehensive claim information display
  - Claim status and key dates section
  - Action buttons for claim management
  - Related documents and timeline sections
  - Policy information related to the claim
  - Responsive design with Tailwind CSS utility classes
  - Semantic HTML structure with ARIA labels
  - Back navigation functionality

  Usage:
  <ClaimDetail {selectedClaimId} on:navigate />
-->

<script>
  import { createEventDispatcher } from "svelte";

  export let selectedClaimId = null;

  const dispatch = createEventDispatcher();

  // Extended mock data structure with comprehensive claim information
  const claims = [
    {
      id: "claim-001",
      claimNumber: "CLM-2024-001234",
      type: "Auto",
      status: "Approved",
      amount: 2500.0,
      deductible: 500.0,
      dateReported: "2024-08-15T10:30:00Z",
      dateOfLoss: "2024-08-12T14:45:00Z",
      dateResolved: "2024-09-02T16:20:00Z",
      description: "Minor collision repair - rear-end accident at intersection",
      adjuster: {
        name: "<PERSON>",
        phone: "(*************",
        email: "<EMAIL>",
        id: "ADJ-001",
      },
      policy: {
        id: "pol-001",
        number: "AUTO-2024-001234",
        type: "Auto",
        holder: "John Doe",
      },
      incident: {
        location: "Main St & Oak Ave, Springfield, IL",
        weather: "Clear",
        policeReport: "PR-2024-8901",
        witnesses: 2,
      },
      vehicle: {
        year: 2020,
        make: "Honda",
        model: "Civic",
        vin: "1HGBH41JXMN109186",
        damage: "Rear bumper, trunk, tail lights",
      },
      settlement: {
        totalAmount: 2500.0,
        deductible: 500.0,
        payoutAmount: 2000.0,
        paymentDate: "2024-09-05T00:00:00Z",
        paymentMethod: "Direct Deposit",
      },
    },
    {
      id: "claim-002",
      claimNumber: "CLM-2024-005678",
      type: "Home",
      status: "Processing",
      amount: 8500.0,
      deductible: 1000.0,
      dateReported: "2024-09-22T09:15:00Z",
      dateOfLoss: "2024-09-20T03:30:00Z",
      dateResolved: null,
      description: "Water damage from burst pipe in basement",
      adjuster: {
        name: "Sarah Chen",
        phone: "(*************",
        email: "<EMAIL>",
        id: "ADJ-002",
      },
      policy: {
        id: "pol-002",
        number: "HOME-2024-005678",
        type: "Home",
        holder: "Jane Smith",
      },
      incident: {
        location: "123 Maple Street, Springfield, IL",
        cause: "Frozen pipe burst",
        affectedAreas: "Basement, first floor laundry room",
        emergencyServices: "Water extraction company contacted",
      },
      property: {
        address: "123 Maple Street, Springfield, IL 62701",
        yearBuilt: 1995,
        squareFootage: 2400,
        damage: "Flooring, drywall, electrical outlets",
      },
      settlement: {
        totalAmount: 8500.0,
        deductible: 1000.0,
        payoutAmount: null,
        paymentDate: null,
        paymentMethod: null,
      },
    },
    {
      id: "claim-003",
      claimNumber: "CLM-***********",
      type: "Health",
      status: "Denied",
      amount: 1250.0,
      deductible: 250.0,
      dateReported: "2024-07-10T11:45:00Z",
      dateOfLoss: "2024-07-08T00:00:00Z",
      dateResolved: "2024-07-25T14:30:00Z",
      description: "Emergency room visit for sports injury",
      adjuster: {
        name: "Dr. Emily Rodriguez",
        phone: "(*************",
        email: "<EMAIL>",
        id: "ADJ-003",
      },
      policy: {
        id: "pol-004",
        number: "HEALTH-***********",
        type: "Health",
        holder: "Mike Johnson",
      },
      medical: {
        provider: "Springfield General Hospital",
        diagnosis: "Ankle sprain, Grade 2",
        treatment: "X-ray, MRI, physical therapy referral",
        denialReason: "Pre-existing condition exclusion",
      },
      settlement: {
        totalAmount: 1250.0,
        deductible: 250.0,
        payoutAmount: 0.0,
        paymentDate: null,
        paymentMethod: null,
      },
    },
  ];

  // Status color mapping
  const statusColors = {
    Approved: "bg-green-100 text-green-800 border-green-200",
    Processing: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Denied: "bg-red-100 text-red-800 border-red-200",
    "Under Review": "bg-blue-100 text-blue-800 border-blue-200",
    Pending: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Claim type icons
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Health: "🏥",
    Life: "❤️",
    Business: "🏢",
  };

  // Find selected claim
  $: selectedClaim = selectedClaimId
    ? claims.find((claim) => claim.id === selectedClaimId)
    : null;

  // Navigation handler
  function handleBackNavigation() {
    dispatch("navigate", { page: "claim-list" });
  }

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  }

  // Format date
  function formatDate(dateString) {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  }

  // Format date with time
  function formatDateTime(dateString) {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
    });
  }

  // Mock timeline data
  const timeline = [
    {
      id: "timeline-001",
      date: "2024-08-15T10:30:00Z",
      title: "Claim Reported",
      description: "Initial claim filed online",
      type: "reported",
    },
    {
      id: "timeline-002",
      date: "2024-08-16T14:20:00Z",
      title: "Adjuster Assigned",
      description: "Michael Thompson assigned to case",
      type: "assigned",
    },
    {
      id: "timeline-003",
      date: "2024-08-20T09:45:00Z",
      title: "Vehicle Inspection",
      description: "Damage assessment completed",
      type: "inspection",
    },
    {
      id: "timeline-004",
      date: "2024-09-02T16:20:00Z",
      title: "Claim Approved",
      description: "Settlement amount approved for $2,000",
      type: "approved",
    },
  ];

  // Mock documents data
  const documents = [
    {
      id: "doc-001",
      name: "Police Report",
      type: "PDF",
      size: "245 KB",
      date: "2024-08-12",
      category: "incident",
    },
    {
      id: "doc-002",
      name: "Vehicle Photos",
      type: "ZIP",
      size: "2.1 MB",
      date: "2024-08-15",
      category: "evidence",
    },
    {
      id: "doc-003",
      name: "Repair Estimate",
      type: "PDF",
      size: "156 KB",
      date: "2024-08-18",
      category: "estimate",
    },
    {
      id: "doc-004",
      name: "Settlement Letter",
      type: "PDF",
      size: "89 KB",
      date: "2024-09-02",
      category: "settlement",
    },
  ];
</script>

{#if !selectedClaim}
  <!-- No Claim Selected State -->
  <main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go back to claim list"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Claim List
      </button>
    </div>

    <div class="max-w-4xl mx-auto text-center">
      <div class="text-8xl mb-6" aria-hidden="true">🔍</div>
      <h1 class="text-4xl font-bold text-gray-900 mb-4">No Claim Selected</h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
        Please select a claim from the claim list to view detailed information.
      </p>
      <button
        class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go to claim list"
      >
        View Claim List
      </button>
    </div>
  </main>
{:else}
  <!-- Claim Detail Content -->
  <main class="min-h-screen bg-gray-50 py-4 px-4 sm:py-8 sm:px-6 lg:px-8">
    <!-- Back Navigation -->
    <div class="max-w-7xl mx-auto mb-8">
      <button
        class="inline-flex items-center px-4 py-2
               bg-white hover:bg-gray-50
               text-gray-700 font-medium rounded-md
               border border-gray-300
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        on:click={handleBackNavigation}
        aria-label="Go back to claim list"
      >
        <svg
          class="mr-2 w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Claim List
      </button>
    </div>

    <!-- Main Content Container -->
    <div class="max-w-7xl mx-auto">
      <!-- Claim Header -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
      >
        <div
          class="flex flex-col lg:flex-row lg:items-center lg:justify-between"
        >
          <div class="flex items-center mb-4 lg:mb-0">
            <span class="text-4xl mr-4" aria-hidden="true"
              >{typeIcons[selectedClaim.type]}</span
            >
            <div>
              <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">
                {selectedClaim.type} Insurance Claim
              </h1>
              <p class="text-gray-600">Claim #{selectedClaim.claimNumber}</p>
            </div>
          </div>
          <div class="flex flex-col sm:flex-row sm:items-center gap-3">
            <span
              class="px-4 py-2 rounded-full text-sm font-medium border text-center
                     {statusColors[selectedClaim.status]}"
              aria-label="Claim status: {selectedClaim.status}"
            >
              {selectedClaim.status}
            </span>
            <span class="text-2xl font-bold text-gray-900">
              {formatCurrency(selectedClaim.amount)}
            </span>
          </div>
        </div>
      </div>

      <!-- Content Grid -->
      <div class="grid gap-6 lg:grid-cols-3">
        <!-- Left Column - Main Details -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Claim Details Card -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Claim Details
            </h2>
            <div class="space-y-4">
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">
                  Description
                </h3>
                <p class="text-gray-900">{selectedClaim.description}</p>
              </div>

              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Claim Amount
                  </h3>
                  <p class="text-lg font-semibold text-gray-900">
                    {formatCurrency(selectedClaim.amount)}
                  </p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Deductible
                  </h3>
                  <p class="text-lg font-semibold text-gray-900">
                    {formatCurrency(selectedClaim.deductible)}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Date of Loss
                  </h3>
                  <p class="text-gray-900">
                    {formatDate(selectedClaim.dateOfLoss)}
                  </p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Date Reported
                  </h3>
                  <p class="text-gray-900">
                    {formatDate(selectedClaim.dateReported)}
                  </p>
                </div>
              </div>

              {#if selectedClaim.dateResolved}
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Date Resolved
                  </h3>
                  <p class="text-gray-900">
                    {formatDate(selectedClaim.dateResolved)}
                  </p>
                </div>
              {/if}
            </div>
          </div>

          <!-- Type-Specific Details -->
          {#if selectedClaim.type === "Auto" && selectedClaim.vehicle}
            <div
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                Vehicle Information
              </h2>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Vehicle
                  </h3>
                  <p class="text-gray-900">
                    {selectedClaim.vehicle.year}
                    {selectedClaim.vehicle.make}
                    {selectedClaim.vehicle.model}
                  </p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">VIN</h3>
                  <p class="text-gray-900 font-mono text-sm">
                    {selectedClaim.vehicle.vin}
                  </p>
                </div>
                <div class="sm:col-span-2">
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Damage Description
                  </h3>
                  <p class="text-gray-900">{selectedClaim.vehicle.damage}</p>
                </div>
              </div>
            </div>
          {/if}

          {#if selectedClaim.type === "Home" && selectedClaim.property}
            <div
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                Property Information
              </h2>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div class="sm:col-span-2">
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Address
                  </h3>
                  <p class="text-gray-900">{selectedClaim.property.address}</p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Year Built
                  </h3>
                  <p class="text-gray-900">
                    {selectedClaim.property.yearBuilt}
                  </p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Square Footage
                  </h3>
                  <p class="text-gray-900">
                    {selectedClaim.property.squareFootage.toLocaleString()} sq ft
                  </p>
                </div>
                <div class="sm:col-span-2">
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Damage Description
                  </h3>
                  <p class="text-gray-900">{selectedClaim.property.damage}</p>
                </div>
              </div>
            </div>
          {/if}

          {#if selectedClaim.type === "Health" && selectedClaim.medical}
            <div
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                Medical Information
              </h2>
              <div class="space-y-4">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Healthcare Provider
                  </h3>
                  <p class="text-gray-900">{selectedClaim.medical.provider}</p>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">
                      Diagnosis
                    </h3>
                    <p class="text-gray-900">
                      {selectedClaim.medical.diagnosis}
                    </p>
                  </div>
                  <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">
                      Treatment
                    </h3>
                    <p class="text-gray-900">
                      {selectedClaim.medical.treatment}
                    </p>
                  </div>
                </div>
                {#if selectedClaim.medical.denialReason}
                  <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-red-800 mb-1">
                      Denial Reason
                    </h3>
                    <p class="text-red-700">
                      {selectedClaim.medical.denialReason}
                    </p>
                  </div>
                {/if}
              </div>
            </div>
          {/if}

          <!-- Timeline Section -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Claim Timeline
            </h2>
            <div class="space-y-4">
              {#each timeline as event (event.id)}
                <div class="flex items-start space-x-3">
                  <div
                    class="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"
                  ></div>
                  <div class="flex-1">
                    <div class="flex items-center justify-between">
                      <h3 class="text-sm font-medium text-gray-900">
                        {event.title}
                      </h3>
                      <span class="text-xs text-gray-500"
                        >{formatDateTime(event.date)}</span
                      >
                    </div>
                    <p class="text-sm text-gray-600 mt-1">
                      {event.description}
                    </p>
                  </div>
                </div>
              {/each}
            </div>
          </div>
        </div>

        <!-- Right Column - Sidebar -->
        <div class="space-y-6">
          <!-- Policy Information -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Related Policy
            </h2>
            <div class="space-y-3">
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">
                  Policy Number
                </h3>
                <p class="text-gray-900 font-mono text-sm">
                  {selectedClaim.policy.number}
                </p>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">
                  Policy Type
                </h3>
                <p class="text-gray-900">{selectedClaim.policy.type}</p>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">
                  Policy Holder
                </h3>
                <p class="text-gray-900">{selectedClaim.policy.holder}</p>
              </div>
              <button
                class="w-full mt-4 px-4 py-2
                       bg-blue-600 hover:bg-blue-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="View policy details"
              >
                View Policy Details
              </button>
            </div>
          </div>

          <!-- Adjuster Information -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Claims Adjuster
            </h2>
            <div class="space-y-3">
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Name</h3>
                <p class="text-gray-900">{selectedClaim.adjuster.name}</p>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Phone</h3>
                <a
                  href="tel:{selectedClaim.adjuster.phone}"
                  class="text-blue-600 hover:text-blue-800 transition-colors duration-200"
                >
                  {selectedClaim.adjuster.phone}
                </a>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Email</h3>
                <a
                  href="mailto:{selectedClaim.adjuster.email}"
                  class="text-blue-600 hover:text-blue-800 transition-colors duration-200 break-all"
                >
                  {selectedClaim.adjuster.email}
                </a>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">
                  Adjuster ID
                </h3>
                <p class="text-gray-900 font-mono text-sm">
                  {selectedClaim.adjuster.id}
                </p>
              </div>
            </div>
          </div>

          <!-- Settlement Information -->
          {#if selectedClaim.settlement}
            <div
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <h2 class="text-xl font-semibold text-gray-900 mb-4">
                Settlement Details
              </h2>
              <div class="space-y-3">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Total Amount
                  </h3>
                  <p class="text-lg font-semibold text-gray-900">
                    {formatCurrency(selectedClaim.settlement.totalAmount)}
                  </p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-1">
                    Deductible
                  </h3>
                  <p class="text-gray-900">
                    {formatCurrency(selectedClaim.settlement.deductible)}
                  </p>
                </div>
                {#if selectedClaim.settlement.payoutAmount !== null}
                  <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">
                      Payout Amount
                    </h3>
                    <p class="text-lg font-semibold text-green-600">
                      {formatCurrency(selectedClaim.settlement.payoutAmount)}
                    </p>
                  </div>
                {/if}
                {#if selectedClaim.settlement.paymentDate}
                  <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">
                      Payment Date
                    </h3>
                    <p class="text-gray-900">
                      {formatDate(selectedClaim.settlement.paymentDate)}
                    </p>
                  </div>
                {/if}
                {#if selectedClaim.settlement.paymentMethod}
                  <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">
                      Payment Method
                    </h3>
                    <p class="text-gray-900">
                      {selectedClaim.settlement.paymentMethod}
                    </p>
                  </div>
                {/if}
              </div>
            </div>
          {/if}

          <!-- Action Buttons -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Actions</h2>
            <div class="space-y-3">
              <button
                class="w-full px-4 py-2
                       bg-blue-600 hover:bg-blue-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="Upload documents"
              >
                📎 Upload Documents
              </button>
              <button
                class="w-full px-4 py-2
                       bg-green-600 hover:bg-green-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                aria-label="Contact adjuster"
              >
                💬 Contact Adjuster
              </button>
              <button
                class="w-full px-4 py-2
                       bg-gray-600 hover:bg-gray-700
                       text-white font-medium rounded-md
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                aria-label="Download claim summary"
              >
                📄 Download Summary
              </button>
              {#if selectedClaim.status === "Denied"}
                <button
                  class="w-full px-4 py-2
                         bg-orange-600 hover:bg-orange-700
                         text-white font-medium rounded-md
                         transition-colors duration-200
                         focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                  aria-label="Appeal claim decision"
                >
                  ⚖️ Appeal Decision
                </button>
              {/if}
            </div>
          </div>

          <!-- Documents -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Related Documents
            </h2>
            <div class="space-y-3">
              {#each documents as document (document.id)}
                <div
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex-1">
                    <h3 class="text-sm font-medium text-gray-900">
                      {document.name}
                    </h3>
                    <p class="text-xs text-gray-500">
                      {document.type} • {document.size} • {formatDate(
                        document.date,
                      )}
                    </p>
                  </div>
                  <button
                    class="ml-3 text-blue-600 hover:text-blue-800 transition-colors duration-200"
                    aria-label="Download {document.name}"
                  >
                    📥
                  </button>
                </div>
              {/each}
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
{/if}

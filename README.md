# Insurance Portal Application

A comprehensive insurance management platform with a responsive navigation hub, built with Svelte, Vite, and Tailwind CSS.

## Features

### Navigation Hub
- **Landing Page**: Clean, intuitive navigation hub with three main sections
- **Responsive Design**: Adapts to different screen sizes with CSS Grid
  - Mobile (< 640px): Single column layout
  - Tablet (640px - 1024px): 2-column responsive layout
  - Desktop (1024px - 1440px): Optimal 3-column layout
  - Large screens (> 1440px): Maintained spacing and alignment

### Policy Management
- **Policy List**: Comprehensive policy display with responsive card layout
  - Mobile (< 640px): 1 card per row
  - Tablet (640px - 1024px): 2 cards per row
  - Desktop (1024px - 1440px): 3 cards per row
  - Large screens (> 1440px): 4 cards per row
- **Policy Detail**: Detailed policy information (coming soon)

### Claim Management
- **Claim List**: Comprehensive claim display with responsive card layout
  - Mobile (< 640px): 1 card per row
  - Tablet (640px - 1024px): 2 cards per row
  - Desktop (1024px - 1440px): 3 cards per row
  - Large screens (> 1440px): 4 cards per row
- **Claim Detail**: Comprehensive claim information display with:
  - Detailed claim information and status tracking
  - Type-specific details (Auto, Home, Health)
  - Claims adjuster contact information
  - Settlement details and payment tracking
  - Timeline of claim events and updates
  - Related documents and attachments
  - Action buttons for claim management

### Design & Accessibility
- **Professional UI**: Clean, modern interface with hover effects and smooth transitions
- **Accessibility**: ARIA labels, semantic HTML, and proper heading hierarchy
- **Navigation**: Intuitive breadcrumb navigation and back buttons
- **Interactive Elements**: Prominent buttons with clear visual feedback

## Technology Stack

- **Frontend Framework**: Svelte 4
- **Build Tool**: Vite 5
- **Styling**: Tailwind CSS 3
- **Package Manager**: npm

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm

### Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Development

Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

### Build for Production

Create a production build:
```bash
npm run build
```

Preview the production build:
```bash
npm run preview
```

## Project Structure

```
├── src/
│   ├── App.svelte          # Main application component with navigation logic
│   ├── LandingPage.svelte  # Main navigation hub/landing page
│   ├── PolicyList.svelte   # Policy list component
│   ├── PolicyDetail.svelte # Comprehensive policy detail component
│   ├── ClaimList.svelte    # Claim list component
│   ├── ClaimDetail.svelte  # Comprehensive claim detail component
│   ├── main.js            # Application entry point
│   └── app.css            # Global styles and Tailwind imports
├── tests/
│   ├── PolicyDetail.test.md # Policy detail component test plan
│   └── ClaimDetail.test.md  # Claim detail component test plan
├── index.html             # HTML template
├── package.json           # Dependencies and scripts
├── vite.config.js         # Vite configuration
├── tailwind.config.js     # Tailwind CSS configuration
└── postcss.config.js      # PostCSS configuration
```

## Component Documentation

### LandingPage.svelte

The main navigation hub that serves as the application's home page.

**Features:**
- Three prominent navigation buttons (Policy List, Policy Detail, Claim Detail)
- Responsive CSS Grid layout with specified breakpoints
- Semantic HTML structure with ARIA labels for accessibility
- Professional styling with hover effects and transitions
- Support contact information in footer

### PolicyList.svelte

Displays insurance policies in a responsive card layout.

**Features:**
- Responsive CSS Grid layout
- Color-coded status badges
- Currency and date formatting
- Hover effects and transitions
- Accessibility features
- Back navigation to landing page

**Mock Data:**
The component includes 10 sample policies with different types and statuses for demonstration purposes.

### ClaimList.svelte

Displays insurance claims in a responsive card layout.

**Features:**
- Responsive CSS Grid layout
- Color-coded status badges (Approved, Processing, Denied, Under Review)
- Currency and date formatting
- Hover effects and transitions
- Accessibility features
- Back navigation to landing page

**Mock Data:**
The component includes 6 sample claims with different types and statuses for demonstration purposes.

### ClaimDetail.svelte

Comprehensive claim detail component that displays detailed claim information.

**Features:**
- Comprehensive claim information display
- Claim status and key dates section
- Type-specific details (Auto, Home, Health insurance)
- Claims adjuster contact information
- Settlement details and payment tracking
- Timeline of claim events and updates
- Related documents and attachments
- Action buttons for claim management
- Responsive design with Tailwind CSS utility classes
- Semantic HTML structure with ARIA labels
- Back navigation functionality

**Mock Data:**
The component includes 3 detailed sample claims with comprehensive information:
- Auto insurance claim (Approved status)
- Home insurance claim (Processing status)
- Health insurance claim (Denied status)

### PolicyDetail.svelte

Comprehensive policy detail component (previously implemented).

**Features:**
- Detailed policy information display
- Policy status and key dates
- Coverage details and beneficiary information
- Related documents and claims
- Professional responsive design

## Navigation

The application uses a simple state-based navigation system:

1. **Landing Page** (`/`): Main navigation hub with four prominent buttons
2. **Policy List**: Accessible from the landing page, displays all insurance policies
3. **Policy Detail**: Comprehensive policy information display with detailed coverage information
4. **Claim List**: Accessible from the landing page, displays all insurance claims
5. **Claim Detail**: Comprehensive claim information display with detailed tracking and management

### Navigation Features:
- **Breadcrumb Navigation**: Shows current page in the header
- **Back Buttons**: Easy navigation back to the landing page
- **Clickable Logo**: Always returns to the landing page
- **Keyboard Navigation**: Full keyboard accessibility support

## Customization

### Adding New Policies

To add new policies, modify the `policies` array in `src/PolicyList.svelte`:

```javascript
const policies = [
  {
    id: 'unique-id',
    policyNumber: 'POLICY-NUMBER',
    type: 'Auto' | 'Home' | 'Life' | 'Health' | 'Business',
    coverageAmount: 250000,
    monthlyPremium: 185.50,
    status: 'Active' | 'Expired' | 'Pending' | 'Cancelled',
    expirationDate: '2025-06-15T00:00:00Z',
    description: 'Policy description...'
  }
  // ... more policies
];
```

### Adding New Claims

To add new claims, modify the `claims` array in `src/ClaimList.svelte`:

```javascript
const claims = [
  {
    id: 'unique-id',
    claimNumber: 'CLM-YYYY-NNNNNN',
    type: 'Auto' | 'Home' | 'Health' | 'Life' | 'Business',
    status: 'Approved' | 'Processing' | 'Denied' | 'Under Review' | 'Pending',
    amount: 2500.00,
    deductible: 500.00,
    dateReported: '2024-08-15T10:30:00Z',
    dateOfLoss: '2024-08-12T14:45:00Z',
    description: 'Claim description...',
    policyNumber: 'RELATED-POLICY-NUMBER'
  }
  // ... more claims
];
```

### ClaimDetail Component Usage

The ClaimDetail component can be used in different ways:

```svelte
<!-- With specific claim ID -->
<ClaimDetail selectedClaimId="claim-001" on:navigate={handleNavigation} />

<!-- Without claim ID (shows "No Claim Selected" state) -->
<ClaimDetail on:navigate={handleNavigation} />
```

**Navigation Events:**
The component dispatches navigation events that can be handled by the parent:

```javascript
function handleNavigation(event) {
  const { page, claimId } = event.detail;
  // Handle navigation logic
}
```

### Styling

The application uses Tailwind CSS for styling. You can customize the design by:

1. Modifying Tailwind classes in the components
2. Adding custom styles in `src/app.css`
3. Extending the Tailwind configuration in `tailwind.config.js`

## Browser Support

This application supports all modern browsers including:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is open source and available under the MIT License.
